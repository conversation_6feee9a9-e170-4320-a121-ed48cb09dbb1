import { query } from '@/lib/local-db'
import { sendEmail, EmailOptions } from '@/lib/email'

// Create email notification function - moved to top to avoid TDZ
const createCompanyDiscoveryEmail = (
  userEmail: string,
  firstName: string,
  companyName: string,
  companyDomain: string,
  companyId: string
): EmailOptions => {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Your Company is Now Available - BenefitLens</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">🎉 Great News!</h1>
        <p style="color: #f0f0f0; margin: 10px 0 0 0; font-size: 16px;">Your Company is Now Available</p>
      </div>

      <div style="background: white; padding: 30px; border-radius: 0 0 10px 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <h2 style="color: #333; margin-top: 0;">Hello ${firstName}!</h2>

        <p style="font-size: 16px; margin-bottom: 20px;">
          Great news! Your company <strong>${companyName}</strong> is now available on BenefitLens!
        </p>

        <p style="font-size: 16px; margin-bottom: 25px;">
          We noticed you have an email address with the domain @${companyDomain}, which matches ${companyName} in our platform.
        </p>

        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 25px 0;">
          <h3 style="color: #333; margin-top: 0; font-size: 18px;">What you can do now:</h3>
          <ul style="margin: 10px 0; padding-left: 20px; color: #555;">
            <li style="margin-bottom: 8px;">View and verify your company's benefits</li>
            <li style="margin-bottom: 8px;">Help improve benefit accuracy for your colleagues</li>
            <li style="margin-bottom: 8px;">Discover benefits you might not know about</li>
            <li style="margin-bottom: 8px;">Compare with other companies in your industry</li>
          </ul>
        </div>

        <div style="text-align: center; margin: 30px 0;">
          <a href="${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/companies/${companyId}"
             style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 15px 30px;
                    text-decoration: none;
                    border-radius: 25px;
                    font-weight: bold;
                    font-size: 16px;
                    display: inline-block;">
            🏢 View ${companyName} Benefits
          </a>
        </div>

        <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 25px 0;">
          <h3 style="color: #1976d2; margin-top: 0; font-size: 16px;">💡 Pro Tip</h3>
          <p style="color: #424242; margin: 0;">
            Sign in with your @${companyDomain} email to automatically associate with ${companyName}
            and access company-specific features!
          </p>
        </div>

        <p style="font-size: 14px; color: #666; margin-top: 25px;">
          This email was sent because we detected your email domain matches a company in our database.
          If you don't want to receive these notifications, you can unsubscribe in your account settings.
        </p>

        <hr style="border: none; border-top: 1px solid #eee; margin: 25px 0;">

        <p style="color: #999; font-size: 12px; text-align: center;">
          This email was sent to ${userEmail}<br>
          © 2025 BenefitLens - Making workplace benefits transparent
        </p>
      </div>
    </body>
    </html>
  `

  const text = `
    BenefitLens - Your Company is Now Available

    Hello ${firstName},

    Great news! Your company ${companyName} is now available on BenefitLens!

    We noticed you have an email address with the domain @${companyDomain}, which matches ${companyName} in our platform.

    What you can do now:
    - View and verify your company's benefits
    - Help improve benefit accuracy for your colleagues
    - Discover benefits you might not know about
    - Compare with other companies in your industry

    Visit your company page: ${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/companies/${companyId}

    Pro Tip: Sign in with your @${companyDomain} email to automatically associate with ${companyName} and access company-specific features!

    This email was sent because we detected your email domain matches a company in our database. If you don't want to receive these notifications, you can unsubscribe in your account settings.

    This email was sent to ${userEmail}
    © 2025 BenefitLens - Making workplace benefits transparent
  `

  return {
    to: userEmail,
    subject: `🎉 ${companyName} is now available on BenefitLens!`,
    html,
    text,
  }
}

export interface UserDiscoveryResult {
  companyId: string
  companyName: string
  domain: string
  discoveredUsers: string[]
  notificationsSent: number
  errors: string[]
}

/**
 * Discovers existing users with email domains matching a company
 * and sends them notification emails about the new company
 */
export async function discoverAndNotifyUsers(companyId: string): Promise<UserDiscoveryResult> {
  const result: UserDiscoveryResult = {
    companyId,
    companyName: '',
    domain: '',
    discoveredUsers: [],
    notificationsSent: 0,
    errors: []
  }



  try {
    // Get company details
    const companyResult = await query(
      'SELECT id, name, domain FROM companies WHERE id = $1',
      [companyId]
    )

    if (companyResult.rows.length === 0) {
      throw new Error('Company not found')
    }

    const company = companyResult.rows[0]
    result.companyName = company.name
    result.domain = company.domain

    if (!company.domain) {
      result.errors.push('Company has no domain configured')
      return result
    }

    // Find existing users with matching email domain who are not already associated with this company
    const usersResult = await query(
      `SELECT email, first_name, last_name
       FROM users
       WHERE email LIKE $1
       AND (company_id IS NULL OR company_id != $2)`,
      [`%@${company.domain}`, companyId]
    )

    result.discoveredUsers = usersResult.rows.map(user => user.email)

    if (result.discoveredUsers.length === 0) {
      return result
    }

    // Associate users with company and send notification emails
    for (const user of usersResult.rows) {
      try {
        // Associate user with company via users.company_id
        await query(
          'UPDATE users SET company_id = $1 WHERE email = $2',
          [companyId, user.email.toLowerCase()]
        )

        // Send notification email
        const emailOptions = createCompanyDiscoveryEmail(
          user.email,
          user.first_name || 'User',
          company.name,
          company.domain,
          companyId
        )

        await sendEmail(emailOptions)
        result.notificationsSent++
      } catch (error) {
        result.errors.push(`Failed to process user ${user.email}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    return result

  } catch (error) {
    result.errors.push(error instanceof Error ? error.message : 'Unknown error')
    return result
  }
}

/**
 * Automatically match a newly registered user to an existing company based on email domain
 * This is called during user registration to provide automatic company association
 */
export async function autoMatchUserToCompany(userEmail: string, firstName: string): Promise<{
  matched: boolean
  companyId?: string
  companyName?: string
  error?: string
}> {
  try {
    const emailDomain = userEmail.split('@')[1]?.toLowerCase()
    if (!emailDomain) {
      return { matched: false, error: 'Invalid email format' }
    }

    // Find company with matching domain
    const companyResult = await query(
      'SELECT id, name, domain FROM companies WHERE LOWER(domain) = $1',
      [emailDomain]
    )

    if (companyResult.rows.length === 0) {
      return { matched: false }
    }

    const company = companyResult.rows[0]

    // Update user's company_id
    await query(
      'UPDATE users SET company_id = $1 WHERE email = $2',
      [company.id, userEmail.toLowerCase()]
    )

    // Send welcome email about company match
    try {
      const emailOptions = createCompanyDiscoveryEmail(
        userEmail,
        firstName,
        company.name,
        company.domain,
        company.id
      )

      await sendEmail(emailOptions)
    } catch (emailError) {
      console.error('Failed to send company match email:', emailError)
      // Don't fail the matching if email fails
    }

    return {
      matched: true,
      companyId: company.id,
      companyName: company.name
    }

  } catch (error) {
    console.error('Error in auto-matching user to company:', error)
    return {
      matched: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}