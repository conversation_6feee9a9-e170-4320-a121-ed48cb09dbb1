apiVersion: v1
kind: ConfigMap
metadata:
  name: benefitlens-config
  namespace: benefitlens
data:
  DATABASE_URL: "****************************************************************/benefitlens"
  USE_LOCAL_AUTH: "true"
  NODE_ENV: "production"
  SMTP_HOST: "localhost"
  SMTP_PORT: "1025"
  FROM_EMAIL: "<EMAIL>"
  ENABLE_PERFORMANCE_MONITORING: "false"
  CACHE_TYPE: "postgresql"
  # Application URLs - CRITICAL for email links to work correctly
  NEXT_PUBLIC_APP_URL: "https://benefitlens.de"
  APP_URL: "https://benefitlens.de"
